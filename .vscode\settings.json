{"tailwindCSS.experimental.classRegex": [["cva\\(((?:[^()]|\\([^()]*\\))*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(((?:[^()]|\\([^()]*\\))*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], "(?:enter|leave)(?:From|To)?=\\s*(?:\"|')([^(?:\"|')]*)", "(?:enter|leave)(?:From|To)?=\\s*(?:\"|'|{`)([^(?:\"|'|`})]*)", ["(?:twMerge|twJoin)\\(([^;]*)[\\);]", "[`'\"`]([^'\"`;]*)[`'\"`]"], "(?:const|let|var)\\s+[\\w$_][_\\w\\d]*\\s*=\\s*['\\\"](.*?)['\\\"]"], "editor.formatOnSave": true, "tailwindCSS.rootFontSize": 10, "i18n-ally.localesPaths": ["apps/h5/src/i18n", "apps/web/src/i18n", "packages/core/src/i18n", "packages/core/src/i18n/messages"]}