import { isUndefined, merge } from 'lodash-es'

import { getPlatformType } from '../envUtil'
import { mergeHeaders } from '../util'

import { PRECISE_RATE_LIMIT, RATE_LIMIT, UN_AUTHORIZED } from './constants'
import GqlError from './gqlError'
import {
  getErrorMessage,
  getErrorType,
  resolveRequestDocument,
  stripIgnoredCharacters,
  stripUndefined,
  TGqlRequestError,
  validateHttpStatus,
} from './util'

export type TGraphqlRequestResponse<D> = {
  status: number
  data: D | null
  error?: string
  errorType?: string
  retryMs?: number
  isError?: boolean
}

/**
 * 封装 GraphQL 请求函数
 */
class GqlRequest {
  #url: string = '/graphql'

  #options: RequestInit = {
    headers: {
      'Content-type': 'application/json; charset=utf-8',
      'x-platform': getPlatformType(),
    },
  }

  constructor(url: string, options?: RequestInit) {
    this.#url = url
    if (options) {
      const { headers: selfHeaders = {}, ...selfOptions } = this.#options
      const { headers = {}, ...restOptions } = options

      // 合并 headers
      const mergedHeaders = mergeHeaders(selfHeaders, headers)

      // 合并 options
      const mergeOptions = merge({}, selfOptions, restOptions)
      this.#options = { ...mergeOptions, headers: mergedHeaders }
    }
  }

  async request<D, V>(
    document: string,
    variables: V,
    options: RequestInit & { url?: string; type?: string },
  ): Promise<TGraphqlRequestResponse<D>> {
    try {
      const { headers: selfHeaders = {}, ...selfOptions } = this.#options
      const { signal, headers = {}, ...restOptions } = options

      // 合并 headers
      const mergedHeaders = mergeHeaders(selfHeaders, headers)

      // 设置平台标识头
      if (mergedHeaders instanceof Headers && !mergedHeaders.has('x-platform')) {
        mergedHeaders.set('x-platform', getPlatformType())
      }

      // 合并 options
      const mergedOptions = merge({}, selfOptions, { url: this.#url }, restOptions)

      // 解析 graphql document
      const resolvedRequestDocument = resolveRequestDocument(document)

      // 处理 method，如果没传 method，则根据 type 自动设置 method 值
      if (mergedOptions?.type === 'query') {
        mergedOptions.method = isUndefined(mergedOptions.method) ? 'GET' : mergedOptions.method
      }

      if (mergedOptions?.type === 'mutation') {
        mergedOptions.method = isUndefined(mergedOptions.method) ? 'POST' : mergedOptions.method
      }

      // 当是 GET 请求时，graphql 参数处理
      if (mergedOptions.method === 'GET') {
        const divider = ~mergedOptions.url.indexOf('?') ? '&' : '?'
        const searchParams = new URLSearchParams()
        if (resolvedRequestDocument.query) {
          searchParams.append('query', stripIgnoredCharacters(resolvedRequestDocument.query))
        }
        if (resolvedRequestDocument.operationName) {
          searchParams.append('operationName', resolvedRequestDocument.operationName)
        }
        if (variables) {
          searchParams.append('variables', JSON.stringify(variables))
        }
        mergedOptions.url = mergedOptions.url + divider + searchParams.toString()
      }

      // 当是 POST 请求时，graphql 参数处理
      if (mergedOptions.method === 'POST') {
        mergedOptions.body = JSON.stringify({
          query: stripIgnoredCharacters(resolvedRequestDocument.query),
          operationName: resolvedRequestDocument.operationName,
          variables,
        })
      }

      // 过滤 headers 中为 undefined 的数据
      const filteredHeaders = new Headers(
        stripUndefined(Object.fromEntries(mergedHeaders.entries())),
      )

      const response = await fetch(mergedOptions.url, {
        signal,
        headers: filteredHeaders,
        ...mergedOptions,
      })

      // 验证请求是否成功响应
      if (validateHttpStatus(response.status)) {
        const data = (await response.json()) as {
          data?: D
          errors?: TGqlRequestError
        }

        // 响应数据中有报错内容，返回报错信息
        if (data?.errors) {
          const errorType = getErrorType(data?.errors)
          const errorMessage = getErrorMessage(data?.errors)

          throw new GqlError(errorMessage, response.status, null, errorType)
        }

        // 响应成功，并且返回了数据
        if (data?.data) {
          return {
            status: response.status,
            data: data.data,
          }
        }
      }

      // 401 状态处理
      if (response.status === 401) {
        throw new GqlError('Unauthorized', response.status, null, UN_AUTHORIZED)
      }

      // 429 限流
      if (response.status === 429) {
        throw new GqlError('当前访问人数过多，请稍后再试！', response.status, null, RATE_LIMIT)
      }

      // 427/428 精准限流
      if ([427, 428].includes(response.status)) {
        const data = (await response.json()) as {
          status?: number
          message?: string
          retry_ms?: number
        }
        const message = data?.message || '当前访问人数过多，请稍后再试！'
        const retryMs = data?.retry_ms || 3000

        throw new GqlError(message, response.status, data, PRECISE_RATE_LIMIT, retryMs)
      }

      throw new GqlError(response.statusText || '获取数据异常，请重试！', response.status, null)
    } catch (error) {
      // 处理自定义 Error 错误
      if (error instanceof GqlError) {
        const { status, message, data, type, retryMs } = error
        return {
          status,
          data: data as null,
          error: message,
          errorType: type,
          retryMs,
          isError: true,
        }
      }

      // 处理特殊错误类型
      const { name } = error as Error
      const resolveMessage =
        name === 'AbortError' ? '获取数据异常，请重试！' : '获取数据异常，请重试！'

      return {
        status: 500,
        data: null,
        error: String(resolveMessage),
        errorType: 'default',
        isError: true,
      }
    }
  }

  async query<D, V>(
    document: string,
    variables?: V,
    options: RequestInit = {},
  ): Promise<TGraphqlRequestResponse<D>> {
    return await this.request(document, variables, {
      type: 'query',
      ...options,
    })
  }

  async mutation<D, V>(
    document: string,
    variables: V,
    options: RequestInit = {},
  ): Promise<TGraphqlRequestResponse<D>> {
    return await this.request(document, variables, {
      type: 'mutation',
      ...options,
    })
  }
}

export default GqlRequest
