'use client'

import { useCallback } from 'react'

import { useLazyGetCustomerQuery } from '../services'
import { clearUserAnalyticsId, setUserAnalyticsId } from '../utils'

/**
 * 埋点相关 hook
 */
const useAnalytics = () => {
  const [getCustomerQuery] = useLazyGetCustomerQuery()

  /**
   * 设置用户埋点标识
   * 从服务器获取用户信息并设置埋点用户标识
   */
  const setAnalyticsUserId = useCallback(async () => {
    try {
      const customerResponse = await getCustomerQuery({}).unwrap()
      const userEmail = customerResponse.customer?.customer_email
      const userPhone = customerResponse.customer?.customer_phone

      setUserAnalyticsId(userEmail, userPhone)

      return {
        success: true,
        userEmail,
        userPhone,
      }
    } catch (error) {
      console.warn('获取用户信息失败，无法设置埋点用户标识:', error)
      return {
        success: false,
        error,
      }
    }
  }, [getCustomerQuery])

  /**
   * 清除用户埋点标识
   */
  const clearAnalyticsUserId = useCallback(() => {
    clearUserAnalyticsId()
  }, [])

  /**
   * 直接设置用户埋点标识（如果已有用户信息）
   */
  const setAnalyticsUserIdDirect = useCallback(
    (userEmail?: string | null, userPhone?: string | null) => {
      setUserAnalyticsId(userEmail, userPhone)
    },
    [],
  )

  return {
    setAnalyticsUserId,
    clearAnalyticsUserId,
    setAnalyticsUserIdDirect,
  }
}

export default useAnalytics
