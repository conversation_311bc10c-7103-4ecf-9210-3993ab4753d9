import { ROUTE } from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { Button } from 'antd'

import { useOrderDetail } from '../context/orderDetailContext'

export default function OrderDetailInfo() {
  const { openPage } = useNavigate()
  const {
    isMigrated,
    orderData,
    getI18nString,
    isFree,
    showShippingAddress,
    migrationOrder,
    copyToClipboard,
  } = useOrderDetail()
  return (
    <div className="space-y-base-48 font-miSansRegular330">
      {/* 配送信息 */}
      <div className="border-t border-gray-base pt-base-48">
        <h2 className="mb-base-32 whitespace-nowrap text-[28px] leading-[1.2]">
          {getI18nString('shipping_info')}
        </h2>
        <div className="space-y-base-16 text-lg">
          {/* 配送方式 */}
          {!isMigrated && (
            <div className="flex gap-base-24">
              <span className="whitespace-nowrap text-gray-3">
                {getI18nString('shipping_method')}：
              </span>
              <span>{orderData?.delivery_method?.method_label}</span>
            </div>
          )}
          {/* 配送地址 */}
          {(isMigrated || showShippingAddress) && (
            <>
              <div className="flex gap-base-24">
                <span className="whitespace-nowrap text-gray-3">收货信息：</span>
                <span>
                  {!isMigrated ? orderData?.shipping_address?.firstname : migrationOrder?.name}
                </span>
                <span>
                  {!isMigrated ? orderData?.shipping_address?.telephone : migrationOrder?.tel}
                </span>
              </div>

              <div className="flex gap-base-24">
                <span className="whitespace-nowrap text-gray-3">收货地址：</span>
                {isMigrated ? (
                  <span>{migrationOrder?.address}</span>
                ) : (
                  <span>
                    {orderData?.shipping_address?.region} {orderData?.shipping_address?.city}{' '}
                    {orderData?.shipping_address?.district}{' '}
                    {orderData?.shipping_address?.street.join()}
                  </span>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* 支付方式 */}
      <div className="border-t border-gray-base pt-base-48">
        <h2 className="mb-base-32 whitespace-nowrap text-[28px] leading-[1.2]">
          {getI18nString('pay_method')}
        </h2>
        <div className="space-y-base-16 text-lg">
          {!isFree && (
            <div className="flex gap-base-24">
              <span className="whitespace-nowrap text-gray-3">{getI18nString('pay_method')}：</span>
              <span>{orderData?.payment_methods?.[0]?.name}</span>
            </div>
          )}
          {!!orderData?.paid_time && (
            <div className="flex gap-base-24">
              <span className="whitespace-nowrap text-gray-3">{getI18nString('pay_time')}：</span>
              <span>{orderData?.paid_time}</span>
            </div>
          )}
        </div>
      </div>

      {/* 发票信息 */}
      {!!orderData?.invoice_info?.type && (
        <div className="border-t border-gray-base pt-base-48">
          <h2 className="mb-base-32 whitespace-nowrap text-[28px] leading-[1.2]">
            {getI18nString('invoice_info')}
          </h2>
          <div className="space-y-base-16 text-lg">
            <div className="flex gap-base-24">
              <span className="whitespace-nowrap text-gray-3">
                {getI18nString('invoice_type')}：
              </span>
              <span>{orderData?.invoice_info?.type}</span>
            </div>

            <div className="flex gap-base-24">
              <span className="whitespace-nowrap text-gray-3">
                {getI18nString('invoice_header')}：
              </span>
              <span>{orderData?.invoice_info?.title}</span>
            </div>
            <div className="flex gap-base-24">
              <span className="whitespace-nowrap text-gray-3">
                {getI18nString('receive_type')}：
              </span>
              <span>{orderData?.invoice_info?.email}</span>
            </div>
            {orderData?.invoice_info?.zlink && (
              <div className="flex gap-base-24">
                <span className="whitespace-nowrap text-gray-3">
                  {getI18nString('invoice_link')}：
                </span>
                <Button
                  style={{ padding: '8px 24px', height: '38px' }}
                  onClick={() => {
                    if (orderData?.invoice_info?.zlink) {
                      copyToClipboard(orderData.invoice_info.zlink)
                      openPage({
                        route: ROUTE.webView,
                        webViewUrl: orderData.invoice_info.zlink,
                      })
                    }
                  }}>
                  {getI18nString('invoice_link')}
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
