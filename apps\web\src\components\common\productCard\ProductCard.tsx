'use client'
import React, { useCallback, useMemo } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'
import {
  cn,
  convertTimeToTimestamp,
  generateEventParams,
  NCoinRange,
  PriceRange,
  type PriceRanges,
  TRACK_EVENT,
  URL_TYPE,
  useVolcAnalytics,
} from '@ninebot/core'
import { HomeProduct } from '@ninebot/core'

import { Link } from '@/i18n/navigation'

/**
 * 产品卡片组件，用于展示产品基本信息
 */
type ProductCardProps = {
  /** 产品数据 */
  product: HomeProduct
  /** 当前时间，用于计算促销状态 */
  currentTime: string
  /** 容器自定义样式 */
  containerStyle?: React.CSSProperties
  /** 图片容器自定义样式 */
  imageContainerStyle?: React.CSSProperties
  /** 产品信息区域自定义样式 */
  infoContainerStyle?: React.CSSProperties
  /** 名称容器自定义样式 */
  nameWrapperStyle?: string
  /** 是否为搜索模态框中的卡片 */
  isSearchModal?: boolean
  /** 关闭模态框的回调函数 */
  closeModal?: () => void
  /** 是否启用图片悬停放大效果 */
  enableImageHoverZoom?: boolean
  /** 是否显示产品类型标签 */
  showProductType?: boolean
  /** 是否将产品信息居中展示 */
  centerInfoContent?: boolean
  /** 标签容器自定义样式 */
  tagContainerStyle?: React.CSSProperties
  /** 标签文本自定义样式 */
  tagTextStyle?: React.CSSProperties
  /** 产品名自定义样式 */
  nameStyle?: string
  /** 价格区域自定义样式 */
  priceContainerStyle?: string
  /** 是否为折扣产品，用于区分埋点事件 */
  isDiscountProduct?: boolean
  /** 页面类型，用于区分不同页面的埋点事件 */
  pageType?: 'home' | 'homeDiscount' | 'category' | 'sort' | 'search' | 'searchResult' | 'cart'
}

// 样式配置，根据是否为搜索模态框模式提供不同尺寸样式
const styles = {
  // 标签样式
  tag: {
    base: 'absolute top-0 flex items-center justify-center rounded-[100px] bg-[#DA291C]',
    modal: 'my-[4px] ml-[4px] px-[6px] py-[4px]',
    normal: 'my-[8px] ml-[8px] px-4 py-2',
    text: {
      base: 'font-miSansSemibold520 text-[#FFFFFF] leading-[120%]',
      modal: 'text-[10px]',
      normal: 'text-[12px]',
    },
  },
  // 缺货提示样式
  outOfStock: {
    base: 'absolute bottom-0 z-10 flex w-full items-center justify-center bg-[#00000014] backdrop-blur-[10px]',
    modal: 'rounded-b-[8px] py-[6px]',
    normal: 'overflow-hidden rounded-b-[20px] py-[12px]',
    text: {
      base: 'text-[#222223]',
      modal: 'font-miSansMedium330 text-[12px] leading-[100%]',
      normal: 'font-miSansMedium380 text-[18px] leading-[120%]',
    },
  },
  // 产品类型样式
  type: {
    base: 'text-[#6E6E73]',
    modal: 'h-[16px] font-miSansMedium380 text-[12px] leading-none',
    normal: 'h-[17px] font-miSansDemiBold450 text-[14px] leading-[120%]',
  },
  // 产品名称样式
  name: {
    base: 'text-black',
    modal: 'line-clamp-1 text-[14px] leading-[140%]',
    normal: 'line-clamp-2 font-miSansDemiBold450 text-[16px] 2xl:text-[20px] leading-[120%]',
  },
  // 价格样式
  price: {
    base: 'font-miSansDemiBold450 leading-[120%]',
    modal: 'text-[14px]',
    normal: 'text-[16px]',
    currency: {
      base: 'font-miSansDemiBold450 leading-[120%]',
      text: 'text-[12px]',
    },
  },
  // 图片样式
  image: {
    normal: '',
    hoverZoom: 'hover:scale-110 transition-transform duration-300 ease-in-out',
  },
}

/**
 * 产品卡片组件
 */
const ProductCard = ({
  product,
  currentTime,
  containerStyle = {},
  imageContainerStyle = {},
  infoContainerStyle = {},
  nameWrapperStyle = '',
  isSearchModal = false,
  closeModal = () => {},
  enableImageHoverZoom = true,
  showProductType = true,
  centerInfoContent = false,
  tagContainerStyle = {},
  tagTextStyle = {},
  nameStyle = '',
  priceContainerStyle = '',
  isDiscountProduct = false,
  pageType = 'home',
}: ProductCardProps) => {
  const getI18nString = useTranslations('Common')
  const { reportEvent } = useVolcAnalytics()

  // 解构产品数据
  const {
    __typename: productType,
    name,
    image,
    price_range: priceRange,
    sku,
    variants = [],
    special_to_date_timestamp: specialToDate = '',
    new_to_date: newToDate = '',
    stock_status: stockStatus,
    custom_attributesV3: customAttributesV3,
  } = product

  const customAttributes = customAttributesV3?.items

  // 边框圆角样式
  const borderRadius = isSearchModal ? 'rounded-[12px]' : 'rounded-[20px]'

  // 图片宽度
  const imageWidth = isSearchModal ? 120 : '100%'

  // 图片样式类
  const imageClassName = enableImageHoverZoom ? styles.image.hoverZoom : styles.image.normal

  // 文本对齐方式
  const textAlignClass = centerInfoContent ? 'text-center' : ''

  // =============== 产品属性计算 ===============

  // 判断是否为折扣商品
  const isDiscount = useMemo(() => {
    if (productType === 'ConfigurableProduct' && variants.length > 0) {
      return variants[0].product.special_to_date_timestamp > currentTime
    }
    return specialToDate ? specialToDate > currentTime : false
  }, [productType, variants, currentTime, specialToDate])

  // 计算价格范围
  const priceRanges = useMemo(() => {
    let currentPriceRange = priceRange
    if (productType === 'ConfigurableProduct' && variants.length > 0) {
      currentPriceRange = variants[0].product.price_range
    }

    if (isDiscount) {
      return currentPriceRange
    }

    return {
      maximum_price: {
        regular_price: currentPriceRange?.maximum_price?.regular_price,
        final_price: currentPriceRange?.maximum_price?.regular_price,
        discount: {
          amount_off: 0,
        },
      },
    }
  }, [productType, variants, priceRange, isDiscount])

  // 判断是否为保险产品
  // const isInsurance = useMemo(() => {
  //   const insurance = customAttributes?.find((item) => item?.code === 'is_insurance')
  //   return insurance && 'value' in insurance ? insurance.value === '1' : false
  // }, [customAttributes])

  // 获取保险链接
  // const insuranceLink = useMemo(() => {
  //   const insuranceLink = customAttributes?.find((item) => item?.code === 'insurance_link')
  //   return insuranceLink && 'value' in insuranceLink ? insuranceLink.value : undefined
  // }, [customAttributes])

  // 获取产品类型
  const productTypeLabel = useMemo(() => {
    const option = customAttributes?.find((item) => item?.code === 'product_tag')
    if (option && 'selected_options' in option) {
      return option.selected_options?.[0]?.label.trim() || ''
    }
    return null
  }, [customAttributes])

  // 判断是否显示N币
  const showNcoin = useMemo(() => {
    if (productType === 'ConfigurableProduct' && variants.length > 0) {
      return variants[0].product.paymeng_method === 'payment_method_ncoin'
    }

    const option = customAttributes?.find((item) => item?.code === 'paymeng_method')
    if (option && 'selected_options' in option) {
      return option.selected_options?.[0]?.value === 'payment_method_ncoin'
    }

    return false
  }, [productType, variants, customAttributes])

  // 判断是否有库存
  const isInStock = useMemo(() => {
    if (productType === 'ConfigurableProduct' && variants?.length > 0) {
      return variants?.some((variant) => variant?.product?.stock_status === 'IN_STOCK')
    }
    return stockStatus === 'IN_STOCK'
  }, [productType, variants, stockStatus])

  // 判断是否显示新品标签
  const showTag = useMemo(() => {
    return convertTimeToTimestamp(newToDate || '') > Number(currentTime)
  }, [currentTime, newToDate])

  // =============== 交互处理 ===============

  // 点击处理
  const handlePress = useCallback(() => {
    // 根据页面类型选择不同的埋点事件
    let trackEvent = TRACK_EVENT.shop_homepage_category_product_picture_click

    switch (pageType) {
      case 'home':
        trackEvent = TRACK_EVENT.shop_homepage_category_product_picture_click
        break
      case 'homeDiscount':
        trackEvent = TRACK_EVENT.shop_homepage_discount_product_picture_click
        break
      case 'category':
        trackEvent = TRACK_EVENT.shop_category_product_picture_click
        break
      case 'sort':
        trackEvent = TRACK_EVENT.shop_sort_product_picture_click
        break
      case 'search':
        trackEvent = TRACK_EVENT.shop_searchpage_product_picture_click
        break
      case 'searchResult':
        trackEvent = TRACK_EVENT.shop_searchresult_product_picture_click
        break
      case 'cart':
        trackEvent = TRACK_EVENT.shop_cart_product_picture_click
        break
      default:
        // 如果没有指定pageType，则根据isDiscountProduct判断
        trackEvent = isDiscountProduct
          ? TRACK_EVENT.shop_homepage_discount_product_picture_click
          : TRACK_EVENT.shop_homepage_category_product_picture_click
    }

    // 上报点击事件
    reportEvent(
      trackEvent,
      generateEventParams({
        type: URL_TYPE.product,
        value: sku || '',
        label: name || '',
      }),
    )

    closeModal()
  }, [sku, closeModal, reportEvent, name, isDiscountProduct, pageType])

  // =============== 渲染函数 ===============

  // 渲染新品标签
  const renderTag = () => {
    if (!showTag) return null

    return (
      <div
        className={`${styles.tag.base} ${isSearchModal ? styles.tag.modal : styles.tag.normal}`}
        style={tagContainerStyle}>
        <div
          className={`${styles.tag.text.base} ${isSearchModal ? styles.tag.text.modal : styles.tag.text.normal}`}
          style={tagTextStyle}>
          {getI18nString('product_tag')}
        </div>
      </div>
    )
  }

  // 渲染缺货提示
  const renderOutOfStock = () => {
    if (isInStock) return null

    return (
      <div
        className={`${styles.outOfStock.base} ${isSearchModal ? styles.outOfStock.modal : styles.outOfStock.normal}`}>
        <div
          className={`${styles.outOfStock.text.base} ${isSearchModal ? styles.outOfStock.text.modal : styles.outOfStock.text.normal}`}>
          {getI18nString('out_of_stock_tip')}
        </div>
      </div>
    )
  }

  // 渲染价格区域
  const renderPrice = () => {
    const textStyle = isSearchModal
      ? `${styles.price.base} ${styles.price.modal}`
      : `${styles.price.base} ${styles.price.normal}`

    return showNcoin ? (
      <NCoinRange
        iconStyle={{ size: isSearchModal ? 14 : 16 }}
        priceRange={priceRanges as PriceRanges}
        textStyle={textStyle}
        originTextStyle={textStyle}
      />
    ) : (
      <PriceRange
        priceRange={priceRanges as PriceRanges}
        currencyStyle={`${styles.price.currency.base} ${styles.price.currency.text}`}
        textStyle={textStyle}
        fractionStyle={`${styles.price.currency.base} ${styles.price.currency.text}`}
        originTextStyle={textStyle}
      />
    )
  }

  return (
    <Link
      className="relative block h-full w-full"
      style={containerStyle}
      onClick={handlePress}
      href={`/${product?.url_key}${product?.url_suffix}`}>
      {/* 产品图片区域 */}
      <div
        className={`relative ${borderRadius} overflow-hidden`}
        style={{
          width: imageWidth,
          aspectRatio: '1/1',
          objectFit: 'contain',
          transition: 'transform 0.3s ease-in-out',
          ...(['home', 'searchResult', 'sort', 'homeDiscount'].includes(pageType) && {
            backgroundColor: '#f3f3f4',
          }),
          ...imageContainerStyle,
        }}>
        <Image
          src={image?.url || ''}
          alt={name || ''}
          className={imageClassName}
          fill
          sizes="100%"
          style={{ objectFit: 'cover' }}
        />
        {renderTag()}
        {renderOutOfStock()}
      </div>

      {/* 产品信息区域 */}
      <div
        className={`${isSearchModal ? 'mt-[8px]' : 'mt-[16px]'} ${centerInfoContent ? 'px-[4px]' : 'pl-[4px]'} ${textAlignClass}`}
        style={infoContainerStyle}>
        {/* 产品类型 */}
        {showProductType ? (
          <div
            className={`${styles.type.base} ${isSearchModal ? styles.type.modal : styles.type.normal}`}>
            {productTypeLabel}
          </div>
        ) : null}

        {/* 产品名称 */}
        {name && (
          <div className={`mt-[8px] ${!isSearchModal ? 'min-h-[48px]' : ''} ${nameWrapperStyle}`}>
            <div
              className={cn(
                `${styles.name.base} ${isSearchModal ? styles.name.modal : styles.name.normal}`,
                nameStyle,
              )}>
              {name}
            </div>
          </div>
        )}

        {/* 产品价格 */}
        <div
          className={cn(
            `mt-[12px] flex items-center ${centerInfoContent ? 'justify-center' : ''}`,
            priceContainerStyle,
          )}>
          {renderPrice()}
        </div>
      </div>
    </Link>
  )
}

export default ProductCard
